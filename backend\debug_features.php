<?php
/**
 * Debug script to check features column data
 */

// Set up environment
$_ENV['DB_HOST'] = 'localhost';
$_ENV['DB_PORT'] = '3306';
$_ENV['DB_NAME'] = 'wolffoxx_ecommerce';
$_ENV['DB_USERNAME'] = 'root';
$_ENV['DB_PASSWORD'] = '';

require_once __DIR__ . '/src/Config/Database.php';
require_once __DIR__ . '/src/Models/BaseModel.php';
require_once __DIR__ . '/src/Models/Product.php';

use Wolffoxx\Config\Database;
use Wolffoxx\Models\Product;

try {
    echo "=== DEBUGGING FEATURES COLUMN ===\n\n";
    
    // Test 1: Direct SQL query
    echo "1. Direct SQL query for product ID 1:\n";
    $stmt = Database::execute("SELECT id, name, material, features FROM products WHERE id = 1 LIMIT 1");
    $directResult = $stmt->fetch();
    
    if ($directResult) {
        echo "✅ Direct query result:\n";
        echo "  ID: " . $directResult['id'] . "\n";
        echo "  Name: " . $directResult['name'] . "\n";
        echo "  Material: " . ($directResult['material'] ?? 'NULL') . "\n";
        echo "  Features: " . ($directResult['features'] ?? 'NULL') . "\n";
        echo "  Features length: " . strlen($directResult['features'] ?? '') . "\n";
    } else {
        echo "❌ No product found with ID 1\n";
    }
    
    echo "\n";
    
    // Test 2: Using Product model
    echo "2. Using Product model:\n";
    $productModel = new Product();
    $product = $productModel->findById(1);
    
    if ($product) {
        echo "✅ Model result:\n";
        echo "  ID: " . $product['id'] . "\n";
        echo "  Name: " . $product['name'] . "\n";
        echo "  Material: " . ($product['material'] ?? 'NULL') . "\n";
        echo "  Features: " . ($product['features'] ?? 'NULL') . "\n";
        echo "  Features length: " . strlen($product['features'] ?? '') . "\n";
    } else {
        echo "❌ No product found using model\n";
    }
    
    echo "\n";
    
    // Test 3: Using getProductDetailsForFrontend
    echo "3. Using getProductDetailsForFrontend:\n";
    $frontendProduct = $productModel->getProductDetailsForFrontend(1);
    
    if ($frontendProduct) {
        echo "✅ Frontend format result:\n";
        echo "  ID: " . $frontendProduct['id'] . "\n";
        echo "  Name: " . $frontendProduct['name'] . "\n";
        echo "  Material: " . ($frontendProduct['material'] ?? 'NULL') . "\n";
        echo "  Features: " . ($frontendProduct['features'] ?? 'NULL') . "\n";
        echo "  Features length: " . strlen($frontendProduct['features'] ?? '') . "\n";
    } else {
        echo "❌ No product found using frontend method\n";
    }
    
    echo "\n";
    
    // Test 4: Check all products with features
    echo "4. All products with features data:\n";
    $stmt = Database::execute("SELECT id, name, features FROM products WHERE features IS NOT NULL AND features != '' LIMIT 5");
    $products = $stmt->fetchAll();
    
    if ($products) {
        foreach ($products as $p) {
            echo "  Product {$p['id']}: {$p['name']} - Features: " . substr($p['features'], 0, 50) . "...\n";
        }
    } else {
        echo "❌ No products found with features data\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

-- Update sample products with material and features data
-- This script adds sample material and features data to existing products

-- Update Oversized products (category_id = 1)
UPDATE products 
SET 
    material = '100% Cotton, Weight - 260 GSM, High Density Screen Print, Pre-Shrunk Fabric',
    features = 'Oversized fit for baggy, Drop shoulder and straight hem, Extra loose, Relaxed silhouette'
WHERE category_id = 1 OR name LIKE '%oversized%' OR name LIKE '%oversize%';

-- Update T-Shirts (category_id = 2)
UPDATE products 
SET 
    material = '100% Cotton, Weight - 260 GSM, High Density Screen Print, Pre-Shrunk Fabric',
    features = 'Regular length, Round neck, Screen Print, Soft-touch breathable fabric'
WHERE category_id = 2 OR (name LIKE '%t-shirt%' OR name LIKE '%tshirt%' OR name LIKE '%tee%') 
AND NOT (name LIKE '%oversized%' OR name LIKE '%oversize%');

-- Update Linen Shirts (category_id = 4 with linen in name)
UPDATE products 
SET 
    material = '100% Linen, Premium quality fabrics, UV protection treatment, Premium airing linen',
    features = 'Turn-down collar, Regular length, Long sleeve, Regular fit'
WHERE category_id = 4 AND name LIKE '%linen%';

-- Update Printed Shirts (category_id = 4 with print in name)
UPDATE products 
SET 
    material = '100% Cotton, Moisture-Absorbent, Lightweight, Soft and Comfortable',
    features = 'Straight Hem, Printed Design, Full sleeves, Regular Fit'
WHERE category_id = 4 AND name LIKE '%print%';

-- Update other Shirts (category_id = 4, not linen or printed)
UPDATE products 
SET 
    material = '100% Cotton, Premium Quality, Breathable Fabric, Durable Construction',
    features = 'Regular Fit, Button-up Design, Collar, Long Sleeves'
WHERE category_id = 4 AND name NOT LIKE '%linen%' AND name NOT LIKE '%print%';

-- Update any remaining products with default values
UPDATE products 
SET 
    material = COALESCE(NULLIF(material, ''), '100% Cotton, Premium Quality'),
    features = COALESCE(NULLIF(features, ''), 'Regular length, Round neck, Premium Quality')
WHERE material IS NULL OR material = '' OR features IS NULL OR features = '';

-- Show updated products
SELECT id, name, category_id, material, features 
FROM products 
WHERE material IS NOT NULL AND features IS NOT NULL
LIMIT 10;

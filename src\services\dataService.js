// Data Service - Handles transition from static data to API data
import { productAPI } from './productAPI.js';
// import { products as staticProducts } from '../data/products_extended.js';
import { getAuthToken } from './authService.js';
import { apiFetch } from './apiFetch';
import { optimizedFetch, batchRequests } from './apiOptimization.js';

// Configuration - ENABLE BACKEND INTEGRATION
const USE_API = true; // ✅ ENABLED - Full backend integration active!

// API Base URL - Use environment variable or fallback
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';

// Data transformation utilities - Updated for YOUR actual API structure
const transformProductFromAPI = (apiProduct) => {


  // Get the regular price
  const price = parseFloat(apiProduct.price) || 0;

  // Get sale price - check all possible fields
  let salePrice = null;
  
  // First try to get sale price from any possible field
  if (apiProduct.sale_price !== undefined && apiProduct.sale_price !== null && !isNaN(parseFloat(apiProduct.sale_price)) && parseFloat(apiProduct.sale_price) > 0) {
    salePrice = parseFloat(apiProduct.sale_price);
  } else if (apiProduct.salePrice !== undefined && apiProduct.salePrice !== null && !isNaN(parseFloat(apiProduct.salePrice)) && parseFloat(apiProduct.salePrice) > 0) {
    salePrice = parseFloat(apiProduct.salePrice);
  } else if (apiProduct.sale_price_at_addition !== undefined && apiProduct.sale_price_at_addition !== null && !isNaN(parseFloat(apiProduct.sale_price_at_addition)) && parseFloat(apiProduct.sale_price_at_addition) > 0) {
    salePrice = parseFloat(apiProduct.sale_price_at_addition);
  } else if (apiProduct.current_sale_price !== undefined && apiProduct.current_sale_price !== null && !isNaN(parseFloat(apiProduct.current_sale_price)) && parseFloat(apiProduct.current_sale_price) > 0) {
    salePrice = parseFloat(apiProduct.current_sale_price);
  }
  
  // If sale price is not less than regular price, it's not a valid sale
  if (salePrice !== null && salePrice >= price) {
    salePrice = null;
  }
  
  // Determine if product is on sale
  const isSale =
    apiProduct.is_sale === 1 ||
    apiProduct.is_sale === '1' ||
    apiProduct.isOnSale === true ||
    apiProduct.on_sale === true ||
    (salePrice !== null && salePrice > 0 && salePrice < price);
  
  // Create the transformed product
  const transformedProduct = {
    // Basic product info
    id: apiProduct.id,
    name: apiProduct.name,
    category: apiProduct.category || 'general',
    price: price,
    
    // Sale information - always set both formats for consistency
    sale_price: salePrice,
    salePrice: salePrice,
    is_sale: isSale ? 1 : 0,
    
    // Keep original values for reference
    original_price: price,
    original_sale_price: salePrice,
    
    // Product details
    description: apiProduct.description || '',
    
    // Variants and images
    colors: Array.isArray(apiProduct.colors) ? apiProduct.colors : [],
    sizes: Array.isArray(apiProduct.sizes) ? apiProduct.sizes : [],
    images: Array.isArray(apiProduct.images) ? apiProduct.images : [],
    
    // Status flags
    isNew: Boolean(apiProduct.isNew || apiProduct.is_new || false),
    isBestSeller: Boolean(apiProduct.isBestSeller || apiProduct.is_best_seller || false),
    isTrending: Boolean(apiProduct.isTrending || apiProduct.is_trending || false),
    
    // Additional metadata
    rating: parseFloat(apiProduct.rating) || 4.5,
    material: apiProduct.material || '100% Cotton',
    features: apiProduct.features || '',
    fit: apiProduct.fit || 'Regular',
    sku: apiProduct.sku || '',
    
    // Stock information
    in_stock: Boolean(apiProduct.in_stock || apiProduct.inStock || false),
    stock_quantity: parseInt(apiProduct.stock_quantity || apiProduct.stockQuantity || 0, 10)
  };
  

  
  return transformedProduct;
};

const transformCategoryFromAPI = (apiCategory) => {
  return {
    id: apiCategory.id,
    name: apiCategory.name,
    slug: apiCategory.slug,
    description: apiCategory.description,
    image_url: apiCategory.image_url,
    product_count: apiCategory.product_count || 0,
    is_active: apiCategory.is_active || true
  };
};

// Data Service
export const dataService = {
  /**
   * Get single order details (includes items) - Using optimized fetch for better performance
   */
  async getOrder(orderId) {
    try {
      const response = await optimizedFetch(`${API_BASE_URL}/orders/${orderId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error('Failed to fetch order details');
      }
      const data = await response.json();
      return data.data;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Batch load multiple products for better performance
   */
  async batchLoadProducts(productIds) {
    if (!USE_API || !productIds.length) {
      return productIds.map(id => staticProducts.find(p => p.id === parseInt(id))).filter(Boolean);
    }

    try {
      const requests = productIds.map(id => ({
        url: `${API_BASE_URL}/products/${id}`,
        options: { method: 'GET' }
      }));

      const results = await batchRequests(requests);
      const products = [];

      for (const result of results) {
        if (result.status === 'fulfilled' && result.value.ok) {
          const data = await result.value.json();
          products.push(transformProductFromAPI(data.data || data));
        }
      }

      return products;
    } catch (error) {
      console.error('Batch load failed:', error);
      return productIds.map(id => staticProducts.find(p => p.id === parseInt(id))).filter(Boolean);
    }
  },
  /**
   * Get all products with filtering and pagination
   */
  async getProducts(filters = {}, page = 1, perPage = 20) {
    if (!USE_API) {
      // Return static data with basic filtering
      let filteredProducts = [...staticProducts];
      
      // Apply basic filters
      if (filters.category) {
        filteredProducts = filteredProducts.filter(p => 
          p.category.toLowerCase().includes(filters.category.toLowerCase())
        );
      }
      
      if (filters.search) {
        filteredProducts = filteredProducts.filter(p => 
          p.name.toLowerCase().includes(filters.search.toLowerCase()) ||
          p.description.toLowerCase().includes(filters.search.toLowerCase())
        );
      }

      if (filters.new) {
        filteredProducts = filteredProducts.filter(p => p.isNew);
      }

      if (filters.bestseller) {
        filteredProducts = filteredProducts.filter(p => p.isBestSeller);
      }

      // Pagination
      const start = (page - 1) * perPage;
      const end = start + perPage;
      const paginatedProducts = filteredProducts.slice(start, end);

      return {
        products: paginatedProducts,
        total: filteredProducts.length,
        pagination: {
          current_page: page,
          per_page: perPage,
          total_pages: Math.ceil(filteredProducts.length / perPage),
          has_next: end < filteredProducts.length,
          has_prev: page > 1
        }
      };
    }

    try {
      const response = await productAPI.getProducts(filters, page, perPage);
      // Your backend already returns the correct format, just transform if needed
      return {
        products: response.data ? response.data.map(transformProductFromAPI) : response.products.map(transformProductFromAPI),
        total: response.pagination?.total || response.total,
        pagination: response.pagination
      };
    } catch (error) {
      // Fallback to static data
      return this.getProducts(filters, page, perPage);
    }
  },

  /**
   * Get single product by ID
   */
  async getProduct(productId) {
    if (!USE_API) {
      const product = staticProducts.find(p => p.id === parseInt(productId));
      if (!product) {
        throw new Error('Product not found');
      }
      return product;
    }

    try {
      const apiProduct = await productAPI.getProduct(productId);
      return transformProductFromAPI(apiProduct);
    } catch (error) {
      const product = staticProducts.find(p => p.id === parseInt(productId));
      if (!product) {
        throw new Error('Product not found');
      }
      return product;
    }
  },

  /**
   * Get products by category
   */
  async getProductsByCategory(category, filters = {}, page = 1, perPage = 20) {
    if (!USE_API) {
      const filteredProducts = staticProducts.filter(p => 
        p.category.toLowerCase().includes(category.toLowerCase())
      );

      // Pagination
      const start = (page - 1) * perPage;
      const end = start + perPage;
      const paginatedProducts = filteredProducts.slice(start, end);

      return {
        products: paginatedProducts,
        total: filteredProducts.length,
        category: category,
        pagination: {
          current_page: page,
          per_page: perPage,
          total_pages: Math.ceil(filteredProducts.length / perPage),
          has_next: end < filteredProducts.length,
          has_prev: page > 1
        }
      };
    }

    try {
      const response = await productAPI.getProductsByCategory(category, filters, page, perPage);
      return {
        products: response.products.map(transformProductFromAPI),
        total: response.total,
        category: response.category,
        pagination: response.pagination
      };
    } catch (error) {
      return this.getProductsByCategory(category, filters, page, perPage);
    }
  },

  /**
   * Search products
   */
  async searchProducts(query, filters = {}, page = 1, perPage = 20) {
    // Only use static data if API is disabled
    if (!USE_API) {
      
      // Extract search terms
      const searchTerms = query.toLowerCase().split(' ');
      
      // Check if we have a color in the search
      const colorKeywords = [
        'black', 'white', 'gray', 'grey', 'blue', 'red', 'green', 'yellow',
        'orange', 'purple', 'pink', 'brown', 'beige', 'navy', 'maroon',
        'olive', 'teal', 'cyan', 'magenta', 'lime', 'indigo', 'violet',
        'cream', 'khaki', 'tan', 'charcoal', 'silver', 'gold'
      ];
      
      const detectedColor = searchTerms.find(term => colorKeywords.includes(term)) || filters.color;
      
      // Get non-color search terms
      const nonColorTerms = searchTerms.filter(term => !colorKeywords.includes(term));
      

      
      // Filter products based on search terms
      let filteredProducts = staticProducts;
      
      // If we have non-color terms, filter by those first
      if (nonColorTerms.length > 0) {
        filteredProducts = filteredProducts.filter(product => {
          // Check if product matches any non-color term
          return nonColorTerms.some(term => 
            product.name.toLowerCase().includes(term) ||
            product.description.toLowerCase().includes(term) ||
            product.category.toLowerCase().includes(term)
          );
        });
        

      }
      
      // If we have a color in the search, further filter by color
      if (detectedColor) {
        // For color-only searches (when there are no non-color terms),
        // we want to search across all products
        if (nonColorTerms.length === 0) {
          filteredProducts = staticProducts;
        }
        
        // Create a copy of the filtered products before applying color filter
        const beforeColorFilter = [...filteredProducts];
        
        filteredProducts = filteredProducts.filter(product => {
          // Check if product has colors array
          if (product.colors && Array.isArray(product.colors)) {
            return product.colors.some(color => 
              color.name && color.name.toLowerCase().includes(detectedColor)
            );
          }
          // Fallback to checking if color is mentioned in name or description
          return (
            product.name.toLowerCase().includes(detectedColor) ||
            product.description.toLowerCase().includes(detectedColor)
          );
        });
        
        // If no products match the color filter, fall back to the original results
        // but only if we're not doing a color-only search
        if (filteredProducts.length === 0) {
          if (nonColorTerms.length > 0 && beforeColorFilter.length > 0) {
            filteredProducts = beforeColorFilter;
          } else {
            // For color-only searches with no results, try a more lenient approach
            filteredProducts = staticProducts.filter(product => {
              const productText = `${product.name} ${product.description} ${product.category}`.toLowerCase();
              return productText.includes(detectedColor);
            });
          }
        }
      }

      // Pagination
      const start = (page - 1) * perPage;
      const end = start + perPage;
      const paginatedProducts = filteredProducts.slice(start, end);
      


      return {
        products: paginatedProducts,
        total: filteredProducts.length,
        query: query,
        pagination: {
          current_page: page,
          per_page: perPage,
          total_pages: Math.ceil(filteredProducts.length / perPage),
          has_next: end < filteredProducts.length,
          has_prev: page > 1
        }
      };
    }

    try {
      const response = await productAPI.searchProducts(query, filters, page, perPage);
      
      // Handle different response formats
      let productsToTransform = [];
      if (response.products) {
        productsToTransform = response.products;
      } else if (response.data) {
        productsToTransform = response.data;
      } else if (Array.isArray(response)) {
        productsToTransform = response;
      }
      
      const transformedProducts = productsToTransform.map(transformProductFromAPI);

      // If we have a color filter but the API didn't filter by color, do it client-side
      if (filters.color && transformedProducts.length > 0) {
        const colorToFilter = filters.color.toLowerCase();
        
        const colorFilteredProducts = transformedProducts.filter(product => {
          // Check if product has colors array
          if (product.colors && Array.isArray(product.colors)) {
            return product.colors.some(color => 
              color.name && color.name.toLowerCase().includes(colorToFilter)
            );
          }
          // Fallback to checking if color is mentioned in name or description
          return (
            product.name.toLowerCase().includes(colorToFilter) ||
            product.description.toLowerCase().includes(colorToFilter)
          );
        });
        
        // If we found products with the color filter, use those
        if (colorFilteredProducts.length > 0) {
          return {
            products: colorFilteredProducts,
            total: colorFilteredProducts.length,
            query: response.query || query,
            pagination: response.pagination || {}
          };
        }
      }
      
      return {
        products: transformedProducts,
        total: response.total || productsToTransform.length,
        query: response.query || query,
        pagination: response.pagination || {}
      };
    } catch (error) {

      // Fall back to static data search with color filtering
      const searchTerms = query.toLowerCase().split(' ');
      
      // Check if we have a color in the search
      const colorKeywords = [
        'black', 'white', 'gray', 'grey', 'blue', 'red', 'green', 'yellow',
        'orange', 'purple', 'pink', 'brown', 'beige', 'navy', 'maroon',
        'olive', 'teal', 'cyan', 'magenta', 'lime', 'indigo', 'violet',
        'cream', 'khaki', 'tan', 'charcoal', 'silver', 'gold'
      ];
      
      const detectedColor = searchTerms.find(term => colorKeywords.includes(term)) || filters.color;
      
      // Filter products based on search terms
      let filteredProducts = staticProducts.filter(p => 
        p.name.toLowerCase().includes(query.toLowerCase()) ||
        p.description.toLowerCase().includes(query.toLowerCase()) ||
        p.category.toLowerCase().includes(query.toLowerCase())
      );
      
      // If we have a color in the search, further filter by color
      if (detectedColor) {
        console.log(`Fallback filtering by color: ${detectedColor}`);
        
        filteredProducts = filteredProducts.filter(product => {
          // Check if product has colors array
          if (product.colors && Array.isArray(product.colors)) {
            return product.colors.some(color => 
              color.name && color.name.toLowerCase().includes(detectedColor)
            );
          }
          // Fallback to checking if color is mentioned in name or description
          return (
            product.name.toLowerCase().includes(detectedColor) ||
            product.description.toLowerCase().includes(detectedColor)
          );
        });
      }
      
      return {
        products: filteredProducts.slice(0, perPage),
        total: filteredProducts.length,
        query: query,
        pagination: {
          current_page: page,
          per_page: perPage,
          total_pages: Math.ceil(filteredProducts.length / perPage)
        }
      };
    }
  },

  /**
   * Get categories
   */
  async getCategories() {
    if (!USE_API) {
      // Extract unique categories from static products
      const categories = [...new Set(staticProducts.map(p => p.category))].map((name, index) => ({
        id: index + 1,
        name: name,
        slug: name.toLowerCase().replace(/\s+/g, '-'),
        description: `${name} collection`,
        product_count: staticProducts.filter(p => p.category === name).length,
        is_active: true
      }));
      return categories;
    }

    try {
      const apiCategories = await productAPI.getCategories();
      return apiCategories.map(transformCategoryFromAPI);
    } catch (error) {
      return this.getCategories();
    }
  },

  /**
   * Get featured products
   */
  async getFeaturedProducts(limit = 12) {
    if (!USE_API) {
      return staticProducts.filter(p => p.isBestSeller).slice(0, limit);
    }

    try {
      const apiProducts = await productAPI.getFeaturedProducts(limit);
      return apiProducts.map(transformProductFromAPI);
    } catch (error) {
      return staticProducts.filter(p => p.isBestSeller).slice(0, limit);
    }
  },

  /**
   * Get new products
   */
  async getNewProducts(limit = 12) {
    if (!USE_API) {
      return staticProducts.filter(p => p.isNew).slice(0, limit);
    }

    try {
      const apiProducts = await productAPI.getNewProducts(limit);
      return apiProducts.map(transformProductFromAPI);
    } catch (error) {
      return staticProducts.filter(p => p.isNew).slice(0, limit);
    }
  },

  /**
   * Get bestseller products
   */
  async getBestsellerProducts(limit = 12) {
    if (!USE_API) {
      return staticProducts.filter(p => p.isBestSeller).slice(0, limit);
    }

    try {
      const apiProducts = await productAPI.getBestsellerProducts(limit);
      return apiProducts.map(transformProductFromAPI);
    } catch (error) {
      return staticProducts.filter(p => p.isBestSeller).slice(0, limit);
    }
  },

  /**
   * Get sale products
   */
  async getSaleProducts(filters = {}, page = 1, perPage = 20) {
    if (!USE_API) {
      const saleProducts = staticProducts.filter(p => p.is_sale === 1);

      // Pagination
      const start = (page - 1) * perPage;
      const end = start + perPage;
      const paginatedProducts = saleProducts.slice(start, end);

      return {
        products: paginatedProducts,
        total: saleProducts.length,
        pagination: {
          current_page: page,
          per_page: perPage,
          total_pages: Math.ceil(saleProducts.length / perPage),
          has_next: end < saleProducts.length,
          has_prev: page > 1
        }
      };
    }

    try {
      const response = await productAPI.getSaleProducts(filters, page, perPage);
      
      // Handle different response structures
      let productsToTransform = [];
      if (response.products) {
        productsToTransform = response.products;
      } else if (response.data) {
        productsToTransform = response.data;
      } else if (Array.isArray(response)) {
        productsToTransform = response;
      }
      
      const transformedProducts = productsToTransform.map(transformProductFromAPI);
      
      return {
        products: transformedProducts,
        total: response.total || productsToTransform.length,
        pagination: response.pagination || {}
      };
    } catch (error) {
      return this.getSaleProducts(filters, page, perPage);
    }
  },

  /**
   * Get similar products (for "You Might Also Like" section)
   */
  async getSimilarProducts(currentProductId, category, limit = 8) {
    if (!USE_API) {
      // Find similar products based on category, excluding current product
      const similar = staticProducts
        .filter(p => p.id !== parseInt(currentProductId) && p.category === category)
        .slice(0, limit);

      // If not enough products in same category, add some more
      if (similar.length < limit) {
        const additional = staticProducts
          .filter(p => p.id !== parseInt(currentProductId) && !similar.find(s => s.id === p.id))
          .slice(0, limit - similar.length);

        return [...similar, ...additional];
      }

      return similar;
    }

    try {
      // Use category-based filtering to get similar products
      const response = await this.getProductsByCategory(category, {}, 1, limit + 5); // Get a few extra

      // Filter out current product and limit results
      const similarProducts = response.products
        .filter(p => p.id !== parseInt(currentProductId))
        .slice(0, limit);

      // If not enough products in same category, get some bestsellers
      if (similarProducts.length < limit) {
        const bestsellers = await this.getBestsellerProducts(limit);
        const additional = bestsellers
          .filter(p => p.id !== parseInt(currentProductId) && !similarProducts.find(s => s.id === p.id))
          .slice(0, limit - similarProducts.length);

        return [...similarProducts, ...additional];
      }

      return similarProducts;
    } catch (error) {
      // Fallback to static data logic
      const similar = staticProducts
        .filter(p => p.id !== parseInt(currentProductId) && p.category === category)
        .slice(0, limit);

      if (similar.length < limit) {
        const additional = staticProducts
          .filter(p => p.id !== parseInt(currentProductId) && !similar.find(s => s.id === p.id))
          .slice(0, limit - similar.length);

        return [...similar, ...additional];
      }

      return similar;
    }
  },

  /**
   * WISHLIST API METHODS
   */
  async getWishlist(userId) {
    try {
      const response = await apiFetch(`${API_BASE_URL}/wishlist`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error('Failed to fetch wishlist');
      }
      const data = await response.json();
      // Support { data: [...] }, { data: { wishlist: [...] } }, { items: [...] }, or { data: { items: [...] } }
      if (Array.isArray(data.data)) {
        return data.data;
      } else if (data.data && Array.isArray(data.data.wishlist)) {
        return data.data.wishlist;
      } else if (Array.isArray(data.items)) {
        return data.items;
      } else if (data.data && Array.isArray(data.data.items)) {
        return data.data.items;
      } else if (data.items && Array.isArray(data.items.items)) {
        // Defensive: handle { items: { items: [...] } }
        return data.items.items;
      } else {
        return [];
      }
    } catch (error) {
      throw error;
    }
  },

  async addToWishlist(userId, productId, notes = null, priority = 'medium') {
    try {
      const response = await apiFetch(`${API_BASE_URL}/wishlist/items`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ product_id: productId, notes, priority })
      });
      if (!response.ok) {
        throw new Error('Failed to add to wishlist');
      }
      const data = await response.json();
      return data.data;
    } catch (error) {
      throw error;
    }
  },

  async removeFromWishlist(userId, productId) {
    try {
      const response = await apiFetch(`${API_BASE_URL}/wishlist/items/${productId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error('Failed to remove from wishlist');
      }
      return true;
    } catch (error) {
      throw error;
    }
  },

  /**
   * CART API METHODS
   */
  async getCart(userId) {
    try {
      const response = await apiFetch(`${API_BASE_URL}/cart`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch cart');
      }

      const data = await response.json();
      return data.data || { items: [], total_items: 0, subtotal: 0 };
    } catch (error) {
      throw error;
    }
  },

  async addToCart(userId, cartItem) {
    try {
      const token = getAuthToken();


      const response = await apiFetch(`${API_BASE_URL}/cart/items`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(cartItem)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to add to cart: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      throw error;
    }
  },

  async updateCartItem(userId, itemId, quantity) {
    try {
      const response = await apiFetch(`${API_BASE_URL}/cart/items/${itemId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ quantity })
      });

      if (!response.ok) {
        throw new Error('Failed to update cart item');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      throw error;
    }
  },

  async removeFromCart(userId, itemId) {
    try {
      const response = await apiFetch(`${API_BASE_URL}/cart/items/${itemId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to remove from cart');
      }

      return true;
    } catch (error) {
      throw error;
    }
  },

  async clearCart(userId) {
    try {
      const response = await apiFetch(`${API_BASE_URL}/cart/clear`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to clear cart');
      }

      return true;
    } catch (error) {
      throw error;
    }
  },

  /**
   * ORDER API METHODS
   */
  async createOrder(userId, orderData) {
    try {

      
      // Validate required fields
      const requiredFields = [
        'customer_email', 'customer_phone', 'customer_name',
        'shipping_address', 'billing_address', 'payment_method',
        'subtotal', 'total_amount'
      ];
      
      for (const field of requiredFields) {
        if (!orderData[field]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }
      
      // Validate address fields
      const addressFields = ['line1', 'city', 'state', 'postal_code', 'country'];
      for (const addrType of ['shipping_address', 'billing_address']) {
        for (const field of addressFields) {
          if (!orderData[addrType][field] && !orderData[addrType][field === 'postal_code' ? 'zip' : field]) {
            throw new Error(`Missing required address field: ${addrType}.${field}`);
          }
        }
      }
      
      // Format the order data for the API
      const formattedOrderData = {
        ...orderData,
        user_id: userId,
        // Ensure we have all required fields with proper formatting
        shipping_address: {
          ...orderData.shipping_address,
          line1: orderData.shipping_address.line1 || orderData.shipping_address.street,
          postal_code: orderData.shipping_address.postal_code || orderData.shipping_address.zip,
          country: orderData.shipping_address.country || 'India'
        },
        billing_address: {
          ...orderData.billing_address,
          line1: orderData.billing_address.line1 || orderData.billing_address.street,
          postal_code: orderData.billing_address.postal_code || orderData.billing_address.zip,
          country: orderData.billing_address.country || 'India'
        }
      };
      
      // Get auth token to ensure we're authenticated
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication required to create order');
      }
      
      const response = await apiFetch(`${API_BASE_URL}/orders`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(formattedOrderData)
      });
      
      // Check if response is ok
      if (!response.ok) {
        let errorMessage = `Failed to create order: ${response.status}`;
        try {
          const errorData = await response.json();
      errorMessage = errorData.error || errorData.message || errorMessage;
        } catch (e) {
          // If we can't parse JSON, try to get text
          try {
            const errorText = await response.text();
            errorMessage = `${errorMessage} ${errorText}`;
          } catch (textError) {
            // Failed to get error text
          }
        }
        throw new Error(errorMessage);
      }
      
      // Parse the response
      try {
            const data = await response.json();

        // Check if we have the expected data structure
        if (!data || !data.data || !data.data.id) {
          throw new Error('Invalid order response: missing order ID');
        }

        return data.data;
      } catch (parseError) {
        throw new Error('Failed to parse order response');
      }
    } catch (error) {
      throw error;
    }
  },

  async getOrders(userId) {
    try {
      const response = await apiFetch(`${API_BASE_URL}/orders`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error('Failed to fetch orders');
      }
      const data = await response.json();
      return data.data || [];
    } catch (error) {
      throw error;
    }
  },



  /**
   * OUTFIT API METHODS
   */
  async getOutfits(userId) {
    try {
      const response = await apiFetch(`${API_BASE_URL}/outfits`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch outfits');
      }

      const data = await response.json();
      return data.data?.outfits || [];
    } catch (error) {
      throw error;
    }
  },

  async createOutfit(userId, outfitData) {
    try {
      const response = await apiFetch(`${API_BASE_URL}/outfits`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(outfitData)
      });
      if (!response.ok) {
        throw new Error('Failed to create outfit');
      }
      const data = await response.json();
      return data.data?.outfit;
    } catch (error) {
      throw error;
    }
  },

  async getOutfit(userId, outfitId) {
    try {
      const response = await apiFetch(`${API_BASE_URL}/outfits/${outfitId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error('Failed to fetch outfit');
      }
      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Get outfit failed:', error);
      throw error;
    }
  },

  async updateOutfit(userId, outfitId, outfitData) {
    try {
      const response = await apiFetch(`${API_BASE_URL}/outfits/${outfitId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(outfitData)
      });
      if (!response.ok) {
        throw new Error('Failed to update outfit');
      }
      const data = await response.json();
      return data.data?.outfit;
    } catch (error) {

      throw error;
    }
  },

  async deleteOutfit(userId, outfitId) {
    try {
      const response = await apiFetch(`${API_BASE_URL}/outfits/${outfitId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error('Failed to delete outfit');
      }
      return true;
    } catch (error) {

      throw error;
    }
  },

  async addItemToOutfit(userId, outfitId, itemData) {
    try {
      const response = await apiFetch(`${API_BASE_URL}/outfits/${outfitId}/items`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(itemData)
      });
      if (!response.ok) {
        throw new Error('Failed to add item to outfit');
      }
      const data = await response.json();
      return data.data?.outfit;
    } catch (error) {

      throw error;
    }
  },

  async removeItemFromOutfit(userId, outfitId, itemId) {
    try {
      const response = await apiFetch(`${API_BASE_URL}/outfits/${outfitId}/items/${itemId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error('Failed to remove item from outfit');
      }
      const data = await response.json();
      return data.data?.outfit;
    } catch (error) {

      throw error;
    }
  },

  // Review methods
  async getProductReviews(productId, page = 1, limit = 10) {
    try {
      const { reviewAPI } = await import('./reviewAPI.js');
      return await reviewAPI.getProductReviews(productId, page, limit);
    } catch (error) {

      throw error;
    }
  },

  async createReview(productId, reviewData) {
    try {
      const { reviewAPI } = await import('./reviewAPI.js');
      return await reviewAPI.createReview(productId, reviewData);
    } catch (error) {

      throw error;
    }
  },

  async uploadReviewImages(reviewId, images) {
    try {
      const { reviewAPI } = await import('./reviewAPI.js');
      return await reviewAPI.uploadReviewImages(reviewId, images);
    } catch (error) {

      throw error;
    }
  },

  async voteReview(reviewId, vote) {
    try {
      const { reviewAPI } = await import('./reviewAPI.js');
      return await reviewAPI.voteReview(reviewId, vote);
    } catch (error) {

      throw error;
    }
  }
};

// Export configuration for easy toggling
export { USE_API };

<?php

namespace Wolffoxx\Models;

use Wolffoxx\Config\Database;

/**
 * Product Model
 * 
 * Handles product data, variants, images,
 * and product-related database operations.
 */
class Product extends BaseModel
{
    protected string $table = 'products';
    
    protected array $fillable = [
        'uuid',
        'name',
        'slug',
        'description',
        'short_description',
        'price',
        'sale_price',
        'cost_price',
        'sku',
        'category',
        'category_id',
        'subcategory',
        'features',
        'material',
        'fit',
        'care_instructions',
        'stock_quantity',
        'low_stock_threshold',
        'track_inventory',
        'is_active',
        'is_featured',
        'is_new',
        'is_bestseller',
        'is_trending',
        'is_on_sale',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'weight',
        'length',
        'width',
        'height'
    ];

    /**
     * Get products with frontend-compatible format
     */
    public function getProductsForFrontend(array $filters = [], int $page = 1, int $limit = 20): array
    {
        $offset = ($page - 1) * $limit;

        $sql = "SELECT
                    p.*,
                    pi.image_url as primary_image
                FROM products p
                LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                WHERE p.is_active = 1";

        $params = [];

        // Add filters
        if (!empty($filters['category'])) {
            $sql .= " AND p.category = ?";
            $params[] = $filters['category'];
        }

        if (!empty($filters['is_new'])) {
            $sql .= " AND p.is_new = 1";
        }

        if (!empty($filters['is_bestseller'])) {
            $sql .= " AND p.is_bestseller = 1";
        }

        if (!empty($filters['is_trending'])) {
            $sql .= " AND p.is_trending = 1";
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (p.name LIKE ? OR p.description LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        // Add sorting
        $sortBy = $filters['sort_by'] ?? 'created_at';
        $sortOrder = $filters['sort_order'] ?? 'DESC';
        $sql .= " ORDER BY p.{$sortBy} {$sortOrder}";

        // Add pagination
        $sql .= " LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;

        $statement = Database::execute($sql, $params);
        $products = $statement->fetchAll();

        // Format products for frontend
        return array_map([$this, 'formatProductForFrontend'], $products);
    }

    /**
     * Get single product with full details for frontend
     */
    public function getProductDetailsForFrontend(int $productId): ?array
    {
        $product = $this->getProductDetails($productId);
        if (!$product) {
            return null;
        }

        return $this->formatProductForFrontend($product, true);
    }

    /**
     * Format product data to match frontend structure
     */
    public function formatProductForFrontend(array $product, bool $includeFullDetails = false): array
    {
        $formatted = [
            'id' => (int)$product['id'],
            'name' => $product['name'] ?? '',
            'category' => $product['category'] ?? $product['category_name'] ?? '',
            'category_id' => (int)($product['category_id'] ?? 0),
            'price' => (float)($product['price'] ?? 0),
            'description' => $product['description'] ?? '',
            'isNew' => (bool)($product['is_new'] ?? false),
            'isBestSeller' => (bool)($product['is_bestseller'] ?? false),
            'isTrending' => (bool)($product['is_trending'] ?? false),
            'rating' => (float)($product['average_rating'] ?? 4.5),
            'material' => $product['material'] ?? '100% Cotton',
            'features' => $product['features'] ?? '',
            'fit' => $product['fit'] ?? 'Regular',
            'sku' => $product['sku'] ?? ''
        ];

        // Add sale price and is_sale flag
        $salePrice = !empty($product['sale_price']) ? (float)$product['sale_price'] : null;
        $regularPrice = (float)($product['price'] ?? 0);

        // Determine if product is on sale (has valid sale price that's less than regular price)
        $isOnSale = $salePrice !== null && $salePrice > 0 && $salePrice < $regularPrice;

        if ($salePrice !== null) {
            $formatted['salePrice'] = $salePrice;
            $formatted['sale_price'] = $salePrice; // Also include snake_case for compatibility
        }

        // Include is_sale flag for frontend compatibility
        $formatted['is_sale'] = $isOnSale ? 1 : 0;
        $formatted['isOnSale'] = $isOnSale;

        if ($includeFullDetails) {
            // Get colors
            $formatted['colors'] = $this->getProductColorsForFrontend($product['id']);

            // Get sizes
            $formatted['sizes'] = $this->getProductSizesForFrontend($product['id']);

            // Get all images
            $formatted['images'] = $this->getProductImagesForFrontend($product['id']);
        } else {
            // Just get primary image for list view
            $formatted['images'] = [$product['primary_image'] ?? ''];
        }

        return $formatted;
    }

    /**
     * Get product colors in frontend format with color-specific images
     */
    public function getProductColorsForFrontend(int $productId): array
    {
        $sql = "SELECT color_name as name, hex_value as value, sort_order
                FROM product_colors
                WHERE product_id = ? AND is_available = 1
                ORDER BY sort_order";

        $statement = Database::execute($sql, [$productId]);
        $colors = $statement->fetchAll();

        return array_map(function($color) use ($productId) {
            return [
                'name' => $color['name'],
                'value' => $color['value'],
                'images' => $this->getColorSpecificImages($productId, $color['name'])
            ];
        }, $colors);
    }

    /**
     * Get images for specific color variant
     */
    private function getColorSpecificImages(int $productId, string $colorName): array
    {
        // First try to get color-specific images by joining with product_colors
        $sql = "SELECT pi.image_url
                FROM product_images pi
                LEFT JOIN product_colors pc ON pi.color_id = pc.id
                WHERE pi.product_id = ? AND (pc.color_name = ? OR pi.color_id IS NULL)
                ORDER BY pi.color_id DESC, pi.sort_order";

        $statement = Database::execute($sql, [$productId, $colorName]);
        $images = $statement->fetchAll();

        if (empty($images)) {
            // Fallback to general product images
            return $this->getProductImagesForFrontend($productId);
        }

        $imageUrls = array_column($images, 'image_url');

        // If we only have general images (color_id IS NULL), return them
        // If we have color-specific images, return only those
        return !empty($imageUrls) ? $imageUrls : $this->getProductImagesForFrontend($productId);
    }

    /**
     * Get product sizes in frontend format
     */
    public function getProductSizesForFrontend(int $productId): array
    {
        $sql = "SELECT size_name, is_available
                FROM product_sizes
                WHERE product_id = ?
                ORDER BY size_order";

        $statement = Database::execute($sql, [$productId]);
        $sizes = $statement->fetchAll();

        return $sizes;
    }

    /**
     * Get product images for frontend
     */
    public function getProductImagesForFrontend(int $productId): array
    {
        $sql = "SELECT image_url
                FROM product_images
                WHERE product_id = ?
                ORDER BY sort_order";

        $statement = Database::execute($sql, [$productId]);
        $images = $statement->fetchAll();

        return array_column($images, 'image_url');
    }

    protected array $casts = [
        'price' => 'float',
        'sale_price' => 'float',
        'cost_price' => 'float',
        'stock_quantity' => 'integer',
        'low_stock_threshold' => 'integer',
        'track_inventory' => 'boolean',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'is_new' => 'boolean',
        'is_bestseller' => 'boolean',
        'is_trending' => 'boolean',
        'is_on_sale' => 'boolean',
        'average_rating' => 'float',
        'total_reviews' => 'integer',
        'weight' => 'float',
        'length' => 'float',
        'width' => 'float',
        'height' => 'float'
    ];

    /**
     * Get products with filters and pagination - FULL FEATURED
     */
    public function getProducts(array $filters = [], int $page = 1, int $perPage = 20): array
    {
        try {
            $offset = ($page - 1) * $perPage;

            // Full query with proper joins - using your existing categories table!
            // Use subquery to get only ONE primary image per product to avoid duplicates
            $sql = "SELECT p.*,
                           (SELECT pi.image_url FROM product_images pi
                            WHERE pi.product_id = p.id AND pi.is_primary = 1
                            LIMIT 1) as primary_image,
                           c.name as category_name
                    FROM {$this->table} p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.is_active = 1";
            $params = [];

            // Add all the filters your frontend needs
            if (!empty($filters['search'])) {
                $sql .= " AND (p.name LIKE ? OR p.description LIKE ?)";
                $params[] = "%{$filters['search']}%";
                $params[] = "%{$filters['search']}%";
            }

            if (!empty($filters['category'])) {
                $sql .= " AND c.name = ?";
                $params[] = $filters['category'];
            }

            if (!empty($filters['is_new']) || !empty($filters['new'])) {
                $sql .= " AND p.is_new = 1";
            }

            if (!empty($filters['is_bestseller']) || !empty($filters['bestseller'])) {
                $sql .= " AND p.is_bestseller = 1";
            }

            if (!empty($filters['is_on_sale']) || !empty($filters['on_sale']) || !empty($filters['is_sale'])) {
                $sql .= " AND p.is_sale = 1";
            }

            if (!empty($filters['is_featured']) || !empty($filters['featured'])) {
                $sql .= " AND p.is_featured = 1";
            }

            // Add color filtering
            if (!empty($filters['color'])) {
                $sql .= " AND EXISTS (SELECT 1 FROM product_colors pc WHERE pc.product_id = p.id AND pc.color_name LIKE ? AND pc.is_available = 1)";
                $params[] = "%{$filters['color']}%";
            }

            // Add sorting
            $sortBy = $filters['sort_by'] ?? 'created_at';
            $sortOrder = $filters['sort_order'] ?? 'DESC';

            $allowedSortFields = ['id', 'name', 'price', 'created_at', 'average_rating'];
            if (in_array($sortBy, $allowedSortFields)) {
                $sql .= " ORDER BY p.{$sortBy} {$sortOrder}";
            }

            // Add pagination
            $sql .= " LIMIT {$perPage} OFFSET {$offset}";

            $statement = Database::execute($sql, $params);
            $results = $statement->fetchAll();

            // For each product, add colors and sizes for frontend
            $processedResults = [];
            foreach ($results as $product) {
                // Add colors and sizes for list view
                $product['colors'] = $this->getProductColors($product['id']);
                $product['sizes'] = $this->getProductSizes($product['id']);
                $processedResults[] = $this->processProductResult($product);
            }

            return $processedResults;

        } catch (\Exception $e) {
            error_log('Get products failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get product by ID with full details - FULL FEATURED
     */
    public function getProductDetails(int $productId): ?array
    {
        try {
            // Get basic product info
            $product = $this->findById($productId);

            if (!$product) {
                return null;
            }

            // Get product images
            $product['images'] = $this->getProductImages($productId);

            // Get product colors
            $product['colors'] = $this->getProductColors($productId);

            // Get product sizes
            $product['sizes'] = $this->getProductSizes($productId);

            return $this->processProductResult($product);

        } catch (\Exception $e) {
            error_log('Get product details failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get product images
     */
    public function getProductImages(int $productId): array
    {
        try {
            $sql = "SELECT * FROM product_images 
                    WHERE product_id = ? 
                    ORDER BY is_primary DESC, sort_order ASC";
            
            $statement = Database::execute($sql, [$productId]);
            return $statement->fetchAll();

        } catch (\Exception $e) {
            error_log('Get product images failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get product colors
     */
    public function getProductColors(int $productId): array
    {
        try {
            $sql = "SELECT pc.*,
                           GROUP_CONCAT(pi.image_url ORDER BY pi.sort_order) as images
                    FROM product_colors pc
                    LEFT JOIN product_images pi ON pc.product_id = pi.product_id AND pc.id = pi.color_id
                    WHERE pc.product_id = ? AND pc.is_available = 1
                    GROUP BY pc.id
                    ORDER BY pc.sort_order ASC";

            $statement = Database::execute($sql, [$productId]);
            $colors = $statement->fetchAll();

            // Process images for each color
            foreach ($colors as &$color) {
                $color['images'] = $color['images'] ? explode(',', $color['images']) : [];
                // Rename color_name to name for frontend compatibility
                $color['name'] = $color['color_name'];
            }

            return $colors;

        } catch (\Exception $e) {
            error_log('Get product colors failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get product sizes
     */
    public function getProductSizes(int $productId): array
    {
        try {
            $sql = "SELECT * FROM product_sizes
                    WHERE product_id = ?
                    ORDER BY size_order ASC";

            $statement = Database::execute($sql, [$productId]);
            return $statement->fetchAll();

        } catch (\Exception $e) {
            error_log('Get product sizes failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get product tags
     */
    public function getProductTags(int $productId): array
    {
        try {
            $sql = "SELECT pt.* FROM product_tags pt
                    INNER JOIN product_tag_relations ptr ON pt.id = ptr.tag_id
                    WHERE ptr.product_id = ?
                    ORDER BY pt.name ASC";
            
            $statement = Database::execute($sql, [$productId]);
            return $statement->fetchAll();

        } catch (\Exception $e) {
            error_log('Get product tags failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get products by category
     */
    public function getByCategory(string $category, array $filters = [], int $page = 1, int $perPage = 20): array
    {
        $filters['category'] = $category;
        return $this->getProducts($filters, $page, $perPage);
    }

    /**
     * Search products
     */
    public function searchProducts(string $query, array $filters = [], int $page = 1, int $perPage = 20): array
    {
        // Add search filter even if query is empty (for color-only searches)
        $filters['search'] = $query;
        return $this->getProducts($filters, $page, $perPage);
    }

    /**
     * Get product categories - FROM YOUR REAL CATEGORIES TABLE
     */
    public function getCategories(): array
    {
        try {
            $sql = "SELECT * FROM categories WHERE is_active = 1 ORDER BY sort_order ASC, name ASC";

            $statement = Database::execute($sql);
            $results = $statement->fetchAll();

            // Format for frontend
            return array_map(function($row) {
                return [
                    'id' => (int)$row['id'],
                    'name' => $row['name'],
                    'slug' => $row['slug'],
                    'description' => $row['description'] ?? '',
                    'image_url' => $row['image_url'] ?? null
                ];
            }, $results);

        } catch (\Exception $e) {
            error_log('Get categories failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get total products count with filters - FULL FEATURED
     */
    public function getTotalCount(array $filters = []): int
    {
        try {
            $sql = "SELECT COUNT(*) as count FROM {$this->table} p
                    LEFT JOIN categories c ON p.category_id = c.id
                    WHERE p.is_active = 1";
            $params = [];

            // Add same filters as getProducts method
            if (!empty($filters['search'])) {
                $sql .= " AND (p.name LIKE ? OR p.description LIKE ?)";
                $params[] = "%{$filters['search']}%";
                $params[] = "%{$filters['search']}%";
            }

            if (!empty($filters['category'])) {
                $sql .= " AND c.name = ?";
                $params[] = $filters['category'];
            }

            if (!empty($filters['is_new']) || !empty($filters['new'])) {
                $sql .= " AND p.is_new = 1";
            }

            if (!empty($filters['is_bestseller']) || !empty($filters['bestseller'])) {
                $sql .= " AND p.is_bestseller = 1";
            }

            if (!empty($filters['is_on_sale']) || !empty($filters['on_sale']) || !empty($filters['is_sale'])) {
                $sql .= " AND p.is_sale = 1";
            }

            if (!empty($filters['is_featured']) || !empty($filters['featured'])) {
                $sql .= " AND p.is_featured = 1";
            }

            // Add color filtering
            if (!empty($filters['color'])) {
                $sql .= " AND EXISTS (SELECT 1 FROM product_colors pc WHERE pc.product_id = p.id AND pc.color_name LIKE ? AND pc.is_available = 1)";
                $params[] = "%{$filters['color']}%";
            }

            $statement = Database::execute($sql, $params);
            $result = $statement->fetch();

            return (int)$result['count'];

        } catch (\Exception $e) {
            error_log('Get total count failed: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Process product result - FRONTEND COMPATIBLE FORMAT
     */
    private function processProductResult(array $product): array
    {
        // Get images - use actual images if available, fallback to placeholder
        $images = [];
        if (isset($product['images']) && !empty($product['images'])) {
            $images = is_array($product['images']) ?
                array_column($product['images'], 'image_url') :
                [$product['images']];
        } elseif (isset($product['primary_image']) && !empty($product['primary_image'])) {
            $images = [$product['primary_image']];
        } else {
            // Use high-quality placeholder images like frontend
            $images = [
                'https://images.unsplash.com/photo-1583743814966-8936f5b7be1a?q=80&w=700&auto=format&fit=crop&ixlib=rb-4.0.3',
                'https://images.unsplash.com/photo-1562157873-818bc0726f68?q=80&w=700&auto=format&fit=crop&ixlib=rb-4.0.3'
            ];
        }

        // Get colors - format exactly like frontend expects
        $colors = [];
        if (isset($product['colors']) && !empty($product['colors'])) {
            foreach ($product['colors'] as $color) {
                $colorImages = [];
                if (isset($color['images']) && !empty($color['images'])) {
                    $colorImages = is_array($color['images']) ? $color['images'] : explode(',', $color['images']);
                    // Remove empty values
                    $colorImages = array_filter($colorImages);
                }

                // If no color-specific images, use general product images
                if (empty($colorImages)) {
                    $colorImages = $images;
                }

                $colors[] = [
                    'name' => $color['name'] ?? $color['color_name'] ?? 'Default',
                    'value' => $color['hex_value'] ?? '#000000',
                    'images' => $colorImages
                ];
            }
        } else {
            // Default colors if none exist
            $colors = [
                [
                    'name' => 'Black',
                    'value' => '#000000',
                    'images' => $images
                ]
            ];
        }

        // Get sizes - use actual sizes if available
        $sizes = [];
        if (isset($product['sizes']) && !empty($product['sizes'])) {
            $sizes = is_array($product['sizes']) ? $product['sizes'] : [];
        } else {
            // Default sizes if none exist
            $sizes = [
                ['size_name' => 'M', 'is_available' => 1],
                ['size_name' => 'L', 'is_available' => 1],
                ['size_name' => 'XL', 'is_available' => 1]
            ];
        }

        // Handle sale price logic
        $salePrice = isset($product['sale_price']) && $product['sale_price'] ? (float)$product['sale_price'] : null;
        $regularPrice = (float)($product['price'] ?? 0);

        // Determine if product is on sale (has valid sale price that's less than regular price)
        $isOnSale = $salePrice !== null && $salePrice > 0 && $salePrice < $regularPrice;

        // Return in EXACT frontend format
        return [
            'id' => (int)$product['id'],
            'name' => $product['name'] ?? '',
            'category' => $product['category_name'] ?? 'general',
            'category_id' => (int)($product['category_id'] ?? 0),
            'price' => $regularPrice,
            'salePrice' => $salePrice,
            'sale_price' => $salePrice, // Also include snake_case for compatibility
            'description' => $product['description'] ?? '',
            'colors' => $colors,
            'sizes' => $sizes,
            'images' => $images,
            'isNew' => (bool)($product['is_new'] ?? false),
            'isBestSeller' => (bool)($product['is_bestseller'] ?? false),
            'isTrending' => (bool)($product['is_trending'] ?? false),
            'isOnSale' => $isOnSale,
            'is_sale' => $isOnSale ? 1 : 0, // Include numeric flag for compatibility
            'rating' => (float)($product['average_rating'] ?? 4.5),
            'material' => $product['material'] ?? '100% Cotton',
            'fit' => $product['fit'] ?? 'Regular',
            'sku' => $product['sku'] ?? '',
            'in_stock' => ($product['stock_quantity'] ?? 0) > 0,
            'stock_quantity' => (int)($product['stock_quantity'] ?? 0)
        ];
    }
}

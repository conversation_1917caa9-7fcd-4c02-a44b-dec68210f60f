<?php
/**
 * Test API response directly
 */

// Simulate the API call
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/api/v1/products/1';

// Set up environment variables
putenv('DB_HOST=localhost');
putenv('DB_PORT=3306');
putenv('DB_NAME=wolffoxx_ecommerce');
putenv('DB_USERNAME=root');
putenv('DB_PASSWORD=');

// Capture output
ob_start();

try {
    // Include the main API file
    include __DIR__ . '/public/index.php';
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}

$output = ob_get_clean();

echo "=== API RESPONSE ===\n";
echo $output;
echo "\n===================\n";

// Try to decode JSON and check for features
if ($output) {
    $data = json_decode($output, true);
    if ($data && isset($data['data'])) {
        echo "\nFeatures field in response: ";
        if (isset($data['data']['features'])) {
            echo "'" . $data['data']['features'] . "'";
        } else {
            echo "NOT FOUND";
        }
        echo "\n";
        
        echo "Material field in response: ";
        if (isset($data['data']['material'])) {
            echo "'" . $data['data']['material'] . "'";
        } else {
            echo "NOT FOUND";
        }
        echo "\n";
    }
}
